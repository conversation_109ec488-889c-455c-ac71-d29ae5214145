{"key": "lastEvents", "content": {"boot": {"body": {"eventType": "boot", "eventId": "HlLV9EhuNANs3KFKNLtkK", "sessionId": "C1p6Ja9FxTBsMK8MjAGc9", "payload": {"eventType": "dev"}, "context": {"inCI": false, "isTTY": true, "platform": "Windows", "nodeVersion": "22.14.0", "cliVersion": "8.6.14"}}, "timestamp": 1752522953780}, "version-update": {"body": {"eventType": "version-update", "eventId": "I4LyAsUMhD-o4UqMWXlZE", "sessionId": "C1p6Ja9FxTBsMK8MjAGc9", "metadata": {"generatedAt": 1752521813184, "userSince": 1750032998741, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": true, "hasStorybookEslint": false, "refCount": 0, "metaFramework": {"name": "Next", "packageName": "next", "version": "15.3.2"}, "testPackages": {}, "hasRouterPackage": true, "monorepo": "Turborepo", "packageManager": {"type": "pnpm", "version": "10.11.0", "agent": "pnpm"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/nextjs", "options": {}}, "builder": "@storybook/builder-webpack5", "renderer": "@storybook/react", "portableStoriesFileCount": 0, "applicationFileCount": 0, "storybookVersion": "8.6.14", "storybookVersionSpecifier": "^8.6.14", "language": "typescript", "storybookPackages": {"@storybook/addon-actions": {"version": "8.6.14"}, "@chromatic-com/storybook": {"version": "3.2.6"}, "@storybook/addon-essentials": {"version": "8.6.14"}, "@storybook/addon-interactions": {"version": "8.6.14"}, "@storybook/addon-onboarding": {"version": "8.6.14"}, "@storybook/addon-themes": {"version": "8.6.14"}, "@storybook/blocks": {"version": "8.6.14"}, "@storybook/nextjs": {"version": "8.6.14"}, "@storybook/react": {"version": "8.6.14"}, "@storybook/test": {"version": "8.6.14"}, "storybook": {"version": "8.6.14"}}, "addons": {"$SNIP\\node_modules\\.pnpm\\@storybook+addon-onboarding_bed5fc859d5edc329cd756106f9194c7\\node_modules\\@storybook\\addon-onboarding": {"version": null}, "$SNIP\\node_modules\\.pnpm\\@storybook+addon-essentials_7d811146e17305eace4d334ca69658bd\\node_modules\\@storybook\\addon-essentials": {"version": null}, "$SNIP\\node_modules\\.pnpm\\@chromatic-com+storybook@3._b0439ddf5e18d90ff5caf27ff40ffc9b\\node_modules\\@chromatic-com\\storybook": {"version": null}, "$SNIP\\node_modules\\.pnpm\\@storybook+addon-interactio_86b9f7aef6ac9425324f8e33baa2bdb9\\node_modules\\@storybook\\addon-interactions": {"version": null}, "$SNIP\\node_modules\\.pnpm\\@storybook+addon-themes@8.6_84e43e5773cf6f34b99f3ce571f0d5f4\\node_modules\\@storybook\\addon-themes": {"version": null}, "chromatic": {"version": "12.0.0", "versionSpecifier": "^12.0.0"}}}, "payload": {}, "context": {"inCI": false, "isTTY": true, "platform": "Windows", "nodeVersion": "22.14.0", "cliVersion": "8.6.14", "anonymousId": "2c7c7731e3e421a86ae47c5e46311d4e810f48127558c45a692b3b3bfa36ef76"}}, "timestamp": 1752521815777}, "dev": {"body": {"eventType": "dev", "eventId": "M8th9wSREteldSOYa4wmG", "sessionId": "C1p6Ja9FxTBsMK8MjAGc9", "metadata": {"generatedAt": 1752523016616, "userSince": 1750032998741, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": true, "hasStorybookEslint": false, "refCount": 0, "metaFramework": {"name": "Next", "packageName": "next", "version": "15.3.2"}, "testPackages": {}, "hasRouterPackage": true, "monorepo": "Turborepo", "packageManager": {"type": "pnpm", "version": "10.11.0", "agent": "pnpm"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/nextjs", "options": {}}, "builder": "@storybook/builder-webpack5", "renderer": "@storybook/react", "portableStoriesFileCount": 0, "applicationFileCount": 0, "storybookVersion": "8.6.14", "storybookVersionSpecifier": "^8.6.14", "language": "typescript", "storybookPackages": {"@storybook/addon-actions": {"version": "8.6.14"}, "@chromatic-com/storybook": {"version": "3.2.6"}, "@storybook/addon-essentials": {"version": "8.6.14"}, "@storybook/addon-interactions": {"version": "8.6.14"}, "@storybook/addon-onboarding": {"version": "8.6.14"}, "@storybook/addon-themes": {"version": "8.6.14"}, "@storybook/blocks": {"version": "8.6.14"}, "@storybook/nextjs": {"version": "8.6.14"}, "@storybook/react": {"version": "8.6.14"}, "@storybook/test": {"version": "8.6.14"}, "storybook": {"version": "8.6.14"}}, "addons": {"$SNIP\\node_modules\\.pnpm\\@storybook+addon-onboarding_bed5fc859d5edc329cd756106f9194c7\\node_modules\\@storybook\\addon-onboarding": {"version": null}, "$SNIP\\node_modules\\.pnpm\\@storybook+addon-essentials_7d811146e17305eace4d334ca69658bd\\node_modules\\@storybook\\addon-essentials": {"version": null}, "$SNIP\\node_modules\\.pnpm\\@chromatic-com+storybook@3._b0439ddf5e18d90ff5caf27ff40ffc9b\\node_modules\\@chromatic-com\\storybook": {"version": null}, "$SNIP\\node_modules\\.pnpm\\@storybook+addon-interactio_86b9f7aef6ac9425324f8e33baa2bdb9\\node_modules\\@storybook\\addon-interactions": {"version": null}, "$SNIP\\node_modules\\.pnpm\\@storybook+addon-themes@8.6_84e43e5773cf6f34b99f3ce571f0d5f4\\node_modules\\@storybook\\addon-themes": {"version": null}, "chromatic": {"version": "12.0.0", "versionSpecifier": "^12.0.0"}}}, "payload": {"versionStatus": "cached", "storyIndex": {"storyCount": 118, "componentCount": 46, "pageStoryCount": 0, "playStoryCount": 0, "autodocsCount": 46, "mdxCount": 0, "exampleStoryCount": 0, "exampleDocsCount": 0, "onboardingStoryCount": 0, "onboardingDocsCount": 0, "version": 5}, "storyStats": {"factory": 0, "play": 0, "render": 79, "loaders": 0, "beforeEach": 0, "globals": 0, "tags": 118, "storyFn": 0, "mount": 0, "moduleMock": 0}}, "context": {"inCI": false, "isTTY": true, "platform": "Windows", "nodeVersion": "22.14.0", "cliVersion": "8.6.14", "anonymousId": "2c7c7731e3e421a86ae47c5e46311d4e810f48127558c45a692b3b3bfa36ef76"}}, "timestamp": 1752523018096}}}