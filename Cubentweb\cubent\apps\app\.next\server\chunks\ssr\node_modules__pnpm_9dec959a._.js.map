{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/telemetry/throttler.ts", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/telemetry/collector.ts", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/telemetry/events/component-mounted.ts", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/telemetry/events/method-called.ts", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/telemetry/events/framework-metadata.ts"], "sourcesContent": ["import type { TelemetryEvent } from '@clerk/types';\n\ntype TtlInMilliseconds = number;\n\nconst DEFAULT_CACHE_TTL_MS = 86400000; // 24 hours\n\n/**\n * Manages throttling for telemetry events using the browser's localStorage to\n * mitigate event flooding in frequently executed code paths.\n */\nexport class TelemetryEventThrottler {\n  #storageKey = 'clerk_telemetry_throttler';\n  #cacheTtl = DEFAULT_CACHE_TTL_MS;\n\n  isEventThrottled(payload: TelemetryEvent): boolean {\n    if (!this.#isValidBrowser) {\n      return false;\n    }\n\n    const now = Date.now();\n    const key = this.#generateKey(payload);\n    const entry = this.#cache?.[key];\n\n    if (!entry) {\n      const updatedCache = {\n        ...this.#cache,\n        [key]: now,\n      };\n\n      localStorage.setItem(this.#storageKey, JSON.stringify(updatedCache));\n    }\n\n    const shouldInvalidate = entry && now - entry > this.#cacheTtl;\n    if (shouldInvalidate) {\n      const updatedCache = this.#cache;\n      delete updatedCache[key];\n\n      localStorage.setItem(this.#storageKey, JSON.stringify(updatedCache));\n    }\n\n    return !!entry;\n  }\n\n  /**\n   * Generates a consistent unique key for telemetry events by sorting payload properties.\n   * This ensures that payloads with identical content in different orders produce the same key.\n   */\n  #generateKey(event: TelemetryEvent): string {\n    const { sk: _sk, pk: _pk, payload, ...rest } = event;\n\n    const sanitizedEvent: Omit<TelemetryEvent, 'sk' | 'pk' | 'payload'> & TelemetryEvent['payload'] = {\n      ...payload,\n      ...rest,\n    };\n\n    return JSON.stringify(\n      Object.keys({\n        ...payload,\n        ...rest,\n      })\n        .sort()\n        .map(key => sanitizedEvent[key]),\n    );\n  }\n\n  get #cache(): Record<string, TtlInMilliseconds> | undefined {\n    const cacheString = localStorage.getItem(this.#storageKey);\n\n    if (!cacheString) {\n      return {};\n    }\n\n    return JSON.parse(cacheString);\n  }\n\n  /**\n   * Checks if the browser's localStorage is supported and writable.\n   *\n   * If any of these operations fail, it indicates that localStorage is either\n   * not supported or not writable (e.g., in cases where the storage is full or\n   * the browser is in a privacy mode that restricts localStorage usage).\n   */\n  get #isValidBrowser(): boolean {\n    if (typeof window === 'undefined') {\n      return false;\n    }\n\n    const storage = window.localStorage;\n    if (!storage) {\n      return false;\n    }\n\n    try {\n      const testKey = 'test';\n      storage.setItem(testKey, testKey);\n      storage.removeItem(testKey);\n\n      return true;\n    } catch (err: unknown) {\n      const isQuotaExceededError =\n        err instanceof DOMException &&\n        // Check error names for different browsers\n        (err.name === 'QuotaExceededError' || err.name === 'NS_ERROR_DOM_QUOTA_REACHED');\n\n      if (isQuotaExceededError && storage.length > 0) {\n        storage.removeItem(this.#storageKey);\n      }\n\n      return false;\n    }\n  }\n}\n", "/**\n * The `TelemetryCollector` class handles collection of telemetry events from Clerk <PERSON>. Telemetry is opt-out and can be disabled by setting a CLERK_TELEMETRY_DISABLED environment variable.\n * The `Clerk<PERSON>rovider` also accepts a `telemetry` prop that will be passed to the collector during initialization:\n *\n * ```jsx\n * <ClerkProvider telemetry={false}>\n *    ...\n * </ClerkProvider>\n * ```\n *\n * For more information, please see the telemetry documentation page: https://clerk.com/docs/telemetry\n */\nimport type {\n  InstanceType,\n  TelemetryCollector as TelemetryCollectorInterface,\n  TelemetryEvent,\n  TelemetryEventRaw,\n} from '@clerk/types';\n\nimport { parsePublishableKey } from '../keys';\nimport { isTruthy } from '../underscore';\nimport { TelemetryEventThrottler } from './throttler';\nimport type { TelemetryCollectorOptions } from './types';\n\ntype TelemetryCollectorConfig = Pick<\n  TelemetryCollectorOptions,\n  'samplingRate' | 'disabled' | 'debug' | 'maxBufferSize'\n> & {\n  endpoint: string;\n};\n\ntype TelemetryMetadata = Required<\n  Pick<TelemetryCollectorOptions, 'clerkVersion' | 'sdk' | 'sdkVersion' | 'publishableKey' | 'secretKey'>\n> & {\n  /**\n   * The instance type, derived from the provided publishableKey.\n   */\n  instanceType: InstanceType;\n};\n\nconst DEFAULT_CONFIG: Partial<TelemetryCollectorConfig> = {\n  samplingRate: 1,\n  maxBufferSize: 5,\n  // Production endpoint: https://clerk-telemetry.com\n  // Staging endpoint: https://staging.clerk-telemetry.com\n  // Local: http://localhost:8787\n  endpoint: 'https://clerk-telemetry.com',\n};\n\nexport class TelemetryCollector implements TelemetryCollectorInterface {\n  #config: Required<TelemetryCollectorConfig>;\n  #eventThrottler: TelemetryEventThrottler;\n  #metadata: TelemetryMetadata = {} as TelemetryMetadata;\n  #buffer: TelemetryEvent[] = [];\n  #pendingFlush: any;\n\n  constructor(options: TelemetryCollectorOptions) {\n    this.#config = {\n      maxBufferSize: options.maxBufferSize ?? DEFAULT_CONFIG.maxBufferSize,\n      samplingRate: options.samplingRate ?? DEFAULT_CONFIG.samplingRate,\n      disabled: options.disabled ?? false,\n      debug: options.debug ?? false,\n      endpoint: DEFAULT_CONFIG.endpoint,\n    } as Required<TelemetryCollectorConfig>;\n\n    if (!options.clerkVersion && typeof window === 'undefined') {\n      // N/A in a server environment\n      this.#metadata.clerkVersion = '';\n    } else {\n      this.#metadata.clerkVersion = options.clerkVersion ?? '';\n    }\n\n    // We will try to grab the SDK data lazily when an event is triggered, so it should always be defined once the event is sent.\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    this.#metadata.sdk = options.sdk!;\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    this.#metadata.sdkVersion = options.sdkVersion!;\n\n    this.#metadata.publishableKey = options.publishableKey ?? '';\n\n    const parsedKey = parsePublishableKey(options.publishableKey);\n    if (parsedKey) {\n      this.#metadata.instanceType = parsedKey.instanceType;\n    }\n\n    if (options.secretKey) {\n      // Only send the first 16 characters of the secret key to to avoid sending the full key. We can still query against the partial key.\n      this.#metadata.secretKey = options.secretKey.substring(0, 16);\n    }\n\n    this.#eventThrottler = new TelemetryEventThrottler();\n  }\n\n  get isEnabled(): boolean {\n    if (this.#metadata.instanceType !== 'development') {\n      return false;\n    }\n\n    // In browser or client environments, we most likely pass the disabled option to the collector, but in environments\n    // where environment variables are available we also check for `CLERK_TELEMETRY_DISABLED`.\n    if (this.#config.disabled || (typeof process !== 'undefined' && isTruthy(process.env.CLERK_TELEMETRY_DISABLED))) {\n      return false;\n    }\n\n    // navigator.webdriver is a property generally set by headless browsers that are running in an automated testing environment.\n    // Data from these environments is not meaningful for us and has the potential to produce a large volume of events, so we disable\n    // collection in this case. (ref: https://developer.mozilla.org/en-US/docs/Web/API/Navigator/webdriver)\n    if (typeof window !== 'undefined' && !!window?.navigator?.webdriver) {\n      return false;\n    }\n\n    return true;\n  }\n\n  get isDebug(): boolean {\n    return this.#config.debug || (typeof process !== 'undefined' && isTruthy(process.env.CLERK_TELEMETRY_DEBUG));\n  }\n\n  record(event: TelemetryEventRaw): void {\n    const preparedPayload = this.#preparePayload(event.event, event.payload);\n\n    this.#logEvent(preparedPayload.event, preparedPayload);\n\n    if (!this.#shouldRecord(preparedPayload, event.eventSamplingRate)) {\n      return;\n    }\n\n    this.#buffer.push(preparedPayload);\n\n    this.#scheduleFlush();\n  }\n\n  #shouldRecord(preparedPayload: TelemetryEvent, eventSamplingRate?: number) {\n    return this.isEnabled && !this.isDebug && this.#shouldBeSampled(preparedPayload, eventSamplingRate);\n  }\n\n  #shouldBeSampled(preparedPayload: TelemetryEvent, eventSamplingRate?: number) {\n    const randomSeed = Math.random();\n\n    const toBeSampled =\n      randomSeed <= this.#config.samplingRate &&\n      (typeof eventSamplingRate === 'undefined' || randomSeed <= eventSamplingRate);\n\n    if (!toBeSampled) {\n      return false;\n    }\n\n    return !this.#eventThrottler.isEventThrottled(preparedPayload);\n  }\n\n  #scheduleFlush(): void {\n    // On the server, we want to flush immediately as we have less guarantees about the lifecycle of the process\n    if (typeof window === 'undefined') {\n      this.#flush();\n      return;\n    }\n\n    const isBufferFull = this.#buffer.length >= this.#config.maxBufferSize;\n    if (isBufferFull) {\n      // If the buffer is full, flush immediately to make sure we minimize the chance of event loss.\n      // Cancel any pending flushes as we're going to flush immediately\n      if (this.#pendingFlush) {\n        const cancel = typeof cancelIdleCallback !== 'undefined' ? cancelIdleCallback : clearTimeout;\n        cancel(this.#pendingFlush);\n      }\n      this.#flush();\n      return;\n    }\n\n    // If we have a pending flush, do nothing\n    if (this.#pendingFlush) {\n      return;\n    }\n\n    if ('requestIdleCallback' in window) {\n      this.#pendingFlush = requestIdleCallback(() => {\n        this.#flush();\n      });\n    } else {\n      // This is not an ideal solution, but it at least waits until the next tick\n      this.#pendingFlush = setTimeout(() => {\n        this.#flush();\n      }, 0);\n    }\n  }\n\n  #flush(): void {\n    fetch(new URL('/v1/event', this.#config.endpoint), {\n      method: 'POST',\n      // TODO: We send an array here with that idea that we can eventually send multiple events.\n      body: JSON.stringify({\n        events: this.#buffer,\n      }),\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n      .catch(() => void 0)\n      .then(() => {\n        this.#buffer = [];\n      })\n      .catch(() => void 0);\n  }\n\n  /**\n   * If running in debug mode, log the event and its payload to the console.\n   */\n  #logEvent(event: TelemetryEvent['event'], payload: Record<string, any>) {\n    if (!this.isDebug) {\n      return;\n    }\n\n    if (typeof console.groupCollapsed !== 'undefined') {\n      console.groupCollapsed('[clerk/telemetry]', event);\n      console.log(payload);\n      console.groupEnd();\n    } else {\n      console.log('[clerk/telemetry]', event, payload);\n    }\n  }\n\n  /**\n   * If in browser, attempt to lazily grab the SDK metadata from the Clerk singleton, otherwise fallback to the initially passed in values.\n   *\n   * This is necessary because the sdkMetadata can be set by the host SDK after the TelemetryCollector is instantiated.\n   */\n  #getSDKMetadata() {\n    let sdkMetadata = {\n      name: this.#metadata.sdk,\n      version: this.#metadata.sdkVersion,\n    };\n\n    // @ts-expect-error -- The global window.Clerk type is declared in clerk-js, but we can't rely on that here\n    if (typeof window !== 'undefined' && window.Clerk) {\n      // @ts-expect-error -- The global window.Clerk type is declared in clerk-js, but we can't rely on that here\n      sdkMetadata = { ...sdkMetadata, ...window.Clerk.constructor.sdkMetadata };\n    }\n\n    return sdkMetadata;\n  }\n\n  /**\n   * Append relevant metadata from the Clerk singleton to the event payload.\n   */\n  #preparePayload(event: TelemetryEvent['event'], payload: TelemetryEvent['payload']): TelemetryEvent {\n    const sdkMetadata = this.#getSDKMetadata();\n\n    return {\n      event,\n      cv: this.#metadata.clerkVersion ?? '',\n      it: this.#metadata.instanceType ?? '',\n      sdk: sdkMetadata.name,\n      sdkv: sdkMetadata.version,\n      ...(this.#metadata.publishableKey ? { pk: this.#metadata.publishableKey } : {}),\n      ...(this.#metadata.secretKey ? { sk: this.#metadata.secretKey } : {}),\n      payload,\n    };\n  }\n}\n", "import type { TelemetryEventRaw } from '@clerk/types';\n\nconst EVENT_COMPONENT_MOUNTED = 'COMPONENT_MOUNTED';\nconst EVENT_COMPONENT_OPENED = 'COMPONENT_OPENED';\nconst EVENT_SAMPLING_RATE = 0.1;\n\ntype ComponentMountedBase = {\n  component: string;\n};\n\ntype EventPrebuiltComponent = ComponentMountedBase & {\n  appearanceProp: boolean;\n  elements: boolean;\n  variables: boolean;\n  baseTheme: boolean;\n};\n\ntype EventComponentMounted = ComponentMountedBase & TelemetryEventRaw['payload'];\n\nfunction createPrebuiltComponentEvent(event: typeof EVENT_COMPONENT_MOUNTED | typeof EVENT_COMPONENT_OPENED) {\n  return function (\n    component: string,\n    props?: Record<string, any>,\n    additionalPayload?: TelemetryEventRaw['payload'],\n  ): TelemetryEventRaw<EventPrebuiltComponent> {\n    return {\n      event,\n      eventSamplingRate: EVENT_SAMPLING_RATE,\n      payload: {\n        component,\n        appearanceProp: Bo<PERSON>an(props?.appearance),\n        baseTheme: <PERSON><PERSON><PERSON>(props?.appearance?.baseTheme),\n        elements: <PERSON><PERSON><PERSON>(props?.appearance?.elements),\n        variables: Boolean(props?.appearance?.variables),\n        ...additionalPayload,\n      },\n    };\n  };\n}\n\n/**\n * Helper function for `telemetry.record()`. Create a consistent event object for when a prebuilt (AIO) component is mounted.\n *\n * @param component - The name of the component.\n * @param props - The props passed to the component. Will be filtered to a known list of props.\n * @param additionalPayload - Additional data to send with the event.\n *\n * @example\n * telemetry.record(eventPrebuiltComponentMounted('SignUp', props));\n */\nexport function eventPrebuiltComponentMounted(\n  component: string,\n  props?: Record<string, any>,\n  additionalPayload?: TelemetryEventRaw['payload'],\n): TelemetryEventRaw<EventPrebuiltComponent> {\n  return createPrebuiltComponentEvent(EVENT_COMPONENT_MOUNTED)(component, props, additionalPayload);\n}\n\n/**\n * Helper function for `telemetry.record()`. Create a consistent event object for when a prebuilt (AIO) component is opened as a modal.\n *\n * @param component - The name of the component.\n * @param props - The props passed to the component. Will be filtered to a known list of props.\n * @param additionalPayload - Additional data to send with the event.\n *\n * @example\n * telemetry.record(eventPrebuiltComponentOpened('GoogleOneTap', props));\n */\nexport function eventPrebuiltComponentOpened(\n  component: string,\n  props?: Record<string, any>,\n  additionalPayload?: TelemetryEventRaw['payload'],\n): TelemetryEventRaw<EventPrebuiltComponent> {\n  return createPrebuiltComponentEvent(EVENT_COMPONENT_OPENED)(component, props, additionalPayload);\n}\n\n/**\n * Helper function for `telemetry.record()`. Create a consistent event object for when a component is mounted. Use `eventPrebuiltComponentMounted` for prebuilt components.\n *\n * **Caution:** Filter the `props` you pass to this function to avoid sending too much data.\n *\n * @param component - The name of the component.\n * @param props - The props passed to the component. Ideally you only pass a handful of props here.\n *\n * @example\n * telemetry.record(eventComponentMounted('SignUp', props));\n */\nexport function eventComponentMounted(\n  component: string,\n  props: TelemetryEventRaw['payload'] = {},\n): TelemetryEventRaw<EventComponentMounted> {\n  return {\n    event: EVENT_COMPONENT_MOUNTED,\n    eventSamplingRate: EVENT_SAMPLING_RATE,\n    payload: {\n      component,\n      ...props,\n    },\n  };\n}\n", "import type { TelemetryEventRaw } from '@clerk/types';\n\nconst EVENT_METHOD_CALLED = 'METHOD_CALLED';\n\ntype EventMethodCalled = {\n  method: string;\n} & Record<string, string | number | boolean>;\n\n/**\n * Fired when a helper method is called from a Clerk SDK.\n */\nexport function eventMethodCalled(\n  method: string,\n  payload?: Record<string, unknown>,\n): TelemetryEventRaw<EventMethodCalled> {\n  return {\n    event: EVENT_METHOD_CALLED,\n    payload: {\n      method,\n      ...payload,\n    },\n  };\n}\n", "import type { TelemetryEventRaw } from '@clerk/types';\n\nconst EVENT_FRAMEWORK_METADATA = 'FRAMEWORK_METADATA';\nconst EVENT_SAMPLING_RATE = 0.1;\n\ntype EventFrameworkMetadata = Record<string, string | number | boolean>;\n\n/**\n * Fired when a helper method is called from a Clerk SDK.\n */\nexport function eventFrameworkMetadata(payload: EventFrameworkMetadata): TelemetryEventRaw<EventFrameworkMetadata> {\n  return {\n    event: EVENT_FRAMEWORK_METADATA,\n    eventSamplingRate: EVENT_SAMPLING_RATE,\n    payload,\n  };\n}\n"], "names": ["EVENT_SAMPLING_RATE"], "mappings": ";;;;;;;;;;;;;;;AAIA,IAAM,uBAAuB;AAJ7B,IAAA,aAAA,WAAA,oCAAA,gBAAA,WAAA;AAUO,IAAM,0BAAN,MAA8B;IAA9B,aAAA;QAAA,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAA;QACL,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAA,aAAc;QACd,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAA,WAAY;IAAA;IAEZ,iBAAiB,OAAA,EAAkC;QACjD,IAAI,CAAC,6RAAA,EAAA,IAAA,EAAK,oCAAA,qBAAiB;YACzB,OAAO;QACT;QAEA,MAAM,MAAM,KAAK,GAAA,CAAI;QACrB,MAAM,MAAM,gSAAA,EAAA,IAAA,EAAK,oCAAA,gBAAL,IAAA,CAAA,IAAA,EAAkB;QAC9B,MAAM,sRAAQ,eAAA,EAAA,IAAA,EAAK,oCAAA,YAAA,CAAS,GAAG,CAAA;QAE/B,IAAI,CAAC,OAAO;YACV,MAAM,eAAe;gBACnB,iRAAG,eAAA,EAAA,IAAA,EAAK,oCAAA,UAAA;gBACR,CAAC,GAAG,CAAA,EAAG;YACT;YAEA,aAAa,OAAA,+QAAQ,eAAA,EAAA,IAAA,EAAK,cAAa,KAAK,SAAA,CAAU,YAAY,CAAC;QACrE;QAEA,MAAM,mBAAmB,SAAS,MAAM,sRAAQ,eAAA,EAAA,IAAA,EAAK;QACrD,IAAI,kBAAkB;YACpB,MAAM,6RAAe,eAAA,EAAA,IAAA,EAAK,oCAAA;YAC1B,OAAO,YAAA,CAAa,GAAG,CAAA;YAEvB,aAAa,OAAA,CAAQ,6RAAA,EAAA,IAAA,EAAK,cAAa,KAAK,SAAA,CAAU,YAAY,CAAC;QACrE;QAEA,OAAO,CAAC,CAAC;IACX;AAsEF;AApGE,cAAA,IAAA;AACA,YAAA,IAAA;AAFK,qCAAA,IAAA;AAAA;;;CAAA,GAqCL,iBAAY,SAAC,KAAA,EAA+B;IAC1C,MAAM,EAAE,IAAI,GAAA,EAAK,IAAI,GAAA,EAAK,OAAA,EAAS,GAAG,KAAK,CAAA,GAAI;IAE/C,MAAM,iBAA4F;QAChG,GAAG,OAAA;QACH,GAAG,IAAA;IACL;IAEA,OAAO,KAAK,SAAA,CACV,OAAO,IAAA,CAAK;QACV,GAAG,OAAA;QACH,GAAG,IAAA;IACL,CAAC,EACE,IAAA,CAAK,EACL,GAAA,CAAI,CAAA,MAAO,cAAA,CAAe,GAAG,CAAC;AAErC;AAEI,YAAM,WAAkD;IAC1D,MAAM,cAAc,aAAa,OAAA,+QAAQ,eAAA,EAAA,IAAA,EAAK,YAAW;IAEzD,IAAI,CAAC,aAAa;QAChB,OAAO,CAAC;IACV;IAEA,OAAO,KAAK,KAAA,CAAM,WAAW;AAC/B;AASI,qBAAe,WAAY;IAC7B,IAAI,OAAO,WAAW,kBAAa;QACjC,OAAO;IACT;;IAEA,MAAM,UAAU,OAAO;AAuBzB;;ACtEF,IAAM,iBAAoD;IACxD,cAAc;IACd,eAAe;IAAA,mDAAA;IAAA,wDAAA;IAAA,+BAAA;IAIf,UAAU;AACZ;AA/CA,IAAA,SAAA,iBAAA,WAAA,SAAA,eAAA,+BAAA,iBAAA,oBAAA,kBAAA,UAAA,aAAA,mBAAA;AAiDO,IAAM,qBAAN,MAAgE;IAOrE,YAAY,OAAA,CAAoC;QAP3C,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAA;QACL,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAA;QACA,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAA;QACA,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAA,WAA+B,CAAC;QAChC,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAA,SAA4B,CAAC,CAAA;QAC7B,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAA;QAGE,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,SAAU;YACb,eAAe,QAAQ,aAAA,IAAiB,eAAe,aAAA;YACvD,cAAc,QAAQ,YAAA,IAAgB,eAAe,YAAA;YACrD,UAAU,QAAQ,QAAA,IAAY;YAC9B,OAAO,QAAQ,KAAA,IAAS;YACxB,UAAU,eAAe,QAAA;QAC3B;QAEA,IAAI,CAAC,QAAQ,YAAA,IAAgB,OAAO,SAAW,aAAa;YAE1D,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,YAAA,GAAe;QAChC,OAAO;YACL,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,YAAA,GAAe,QAAQ,YAAA,IAAgB;QACxD;QAIA,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,GAAA,GAAM,QAAQ,GAAA;QAE7B,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,UAAA,GAAa,QAAQ,UAAA;QAEpC,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,cAAA,GAAiB,QAAQ,cAAA,IAAkB;QAE1D,MAAM,0RAAY,sBAAA,EAAoB,QAAQ,cAAc;QAC5D,IAAI,WAAW;YACb,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,YAAA,GAAe,UAAU,YAAA;QAC1C;QAEA,IAAI,QAAQ,SAAA,EAAW;YAErB,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,SAAA,GAAY,QAAQ,SAAA,CAAU,SAAA,CAAU,GAAG,EAAE;QAC9D;QAEA,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,iBAAkB,IAAI,wBAAwB;IACrD;IAEA,IAAI,YAAqB;QACvB,kRAAI,eAAA,EAAA,IAAA,EAAK,WAAU,YAAA,KAAiB,eAAe;YACjD,OAAO;QACT;QAIA,kRAAI,eAAA,EAAA,IAAA,EAAK,SAAQ,QAAA,IAAa,OAAO,YAAY,4RAAe,YAAA,EAAS,QAAQ,GAAA,CAAI,wBAAwB,GAAI;YAC/G,OAAO;QACT;QAKA,IAAI,OAAO,SAAW,eAAe,CAAC,CAAC,QAAQ,WAAW,WAAW;;QAErE;QAEA,OAAO;IACT;IAEA,IAAI,UAAmB;QACrB,qRAAO,eAAA,EAAA,IAAA,EAAK,SAAQ,KAAA,IAAU,OAAO,YAAY,6RAAe,WAAA,EAAS,QAAQ,GAAA,CAAI,qBAAqB;IAC5G;IAEA,OAAO,KAAA,EAAgC;QACrC,MAAM,kBAAkB,gSAAA,EAAA,IAAA,EAAK,+BAAA,mBAAL,IAAA,CAAA,IAAA,EAAqB,MAAM,KAAA,EAAO,MAAM,OAAA;QAEhE,CAAA,GAAA,yQAAA,CAAA,kBAAA,EAAA,IAAA,EAAK,+BAAA,aAAL,IAAA,CAAA,IAAA,EAAe,gBAAgB,KAAA,EAAO;QAEtC,IAAI,+QAAC,kBAAA,EAAA,IAAA,EAAK,+BAAA,iBAAL,IAAA,CAAA,IAAA,EAAmB,iBAAiB,MAAM,iBAAA,GAAoB;YACjE;QACF;QAEA,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,SAAQ,IAAA,CAAK,eAAe;QAEjC,CAAA,GAAA,yQAAA,CAAA,kBAAA,EAAA,IAAA,EAAK,+BAAA,kBAAL,IAAA,CAAA,IAAA;IACF;AAgIF;AAhNE,UAAA,IAAA;AACA,kBAAA,IAAA;AACA,YAAA,IAAA;AACA,UAAA,IAAA;AACA,gBAAA,IAAA;AALK,gCAAA,IAAA;AAmFL,kBAAa,SAAC,eAAA,EAAiC,iBAAA,EAA4B;IACzE,OAAO,IAAA,CAAK,SAAA,IAAa,CAAC,IAAA,CAAK,OAAA,kRAAW,kBAAA,EAAA,IAAA,EAAK,+BAAA,oBAAL,IAAA,CAAA,IAAA,EAAsB,iBAAiB;AACnF;AAEA,qBAAgB,SAAC,eAAA,EAAiC,iBAAA,EAA4B;IAC5E,MAAM,aAAa,KAAK,MAAA,CAAO;IAE/B,MAAM,cACJ,4RAAc,eAAA,EAAA,IAAA,EAAK,SAAQ,YAAA,IAAA,CAC1B,OAAO,sBAAsB,eAAe,cAAc,iBAAA;IAE7D,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAEA,OAAO,CAAC,6RAAA,EAAA,IAAA,EAAK,iBAAgB,gBAAA,CAAiB,eAAe;AAC/D;AAEA,mBAAc,WAAS;IAErB,IAAI,OAAO,WAAW,kBAAa;QACjC,CAAA,GAAA,yQAAA,CAAA,kBAAA,EAAA,IAAA,EAAK,+BAAA,UAAL,IAAA,CAAA,IAAA;QACA;IACF;;IAEA,MAAM,eAAe,mBAAK,SAAQ,UAAU,mBAAK,SAAQ;AA2B3D;AAEA,WAAM,WAAS;IACb,MAAM,IAAI,IAAI,iBAAa,yRAAA,EAAA,IAAA,EAAK,SAAQ,QAAQ,GAAG;QACjD,QAAQ;QAAA,0FAAA;QAER,MAAM,KAAK,SAAA,CAAU;YACnB,QAAQ,6RAAA,EAAA,IAAA,EAAK;QACf,CAAC;QACD,SAAS;YACP,gBAAgB;QAClB;IACF,CAAC,EACE,KAAA,CAAM,IAAM,KAAA,CAAM,EAClB,IAAA,CAAK,MAAM;QACV,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,SAAU,CAAC,CAAA;IAClB,CAAC,EACA,KAAA,CAAM,IAAM,KAAA,CAAM;AACvB;AAAA;;CAAA,GAKA,cAAS,SAAC,KAAA,EAAgC,OAAA,EAA8B;IACtE,IAAI,CAAC,IAAA,CAAK,OAAA,EAAS;QACjB;IACF;IAEA,IAAI,OAAO,QAAQ,cAAA,KAAmB,aAAa;QACjD,QAAQ,cAAA,CAAe,qBAAqB,KAAK;QACjD,QAAQ,GAAA,CAAI,OAAO;QACnB,QAAQ,QAAA,CAAS;IACnB,OAAO;QACL,QAAQ,GAAA,CAAI,qBAAqB,OAAO,OAAO;IACjD;AACF;AAAA;;;;CAAA,GAOA,oBAAe,WAAG;IAChB,IAAI,cAAc;QAChB,MAAM,6RAAA,EAAA,IAAA,EAAK,WAAU,GAAA;QACrB,uRAAS,eAAA,EAAA,IAAA,EAAK,WAAU,UAAA;IAC1B;IAGA,IAAI,OAAO,WAAW,eAAe,EAAc,KAAP;;IAG5C;IAEA,OAAO;AACT;AAAA;;CAAA,GAKA,oBAAe,SAAC,KAAA,EAAgC,OAAA,EAAoD;IAClG,MAAM,4RAAc,kBAAA,EAAA,IAAA,EAAK,+BAAA,mBAAL,IAAA,CAAA,IAAA;IAEpB,OAAO;QACL;QACA,kRAAI,eAAA,EAAA,IAAA,EAAK,WAAU,YAAA,IAAgB;QACnC,iRAAI,gBAAA,EAAA,IAAA,EAAK,WAAU,YAAA,IAAgB;QACnC,KAAK,YAAY,IAAA;QACjB,MAAM,YAAY,OAAA;QAClB,GAAI,6RAAA,EAAA,IAAA,EAAK,WAAU,cAAA,GAAiB;YAAE,kRAAI,eAAA,EAAA,IAAA,EAAK,WAAU,cAAA;QAAe,IAAI,CAAC,CAAA;QAC7E,iRAAI,eAAA,EAAA,IAAA,EAAK,WAAU,SAAA,GAAY;YAAE,kRAAI,eAAA,EAAA,IAAA,EAAK,WAAU,SAAA;QAAU,IAAI,CAAC,CAAA;QACnE;IACF;AACF;;AC/PF,IAAM,0BAA0B;AAChC,IAAM,yBAAyB;AAC/B,IAAM,sBAAsB;AAe5B,SAAS,6BAA6B,KAAA,EAAuE;IAC3G,OAAO,SACL,SAAA,EACA,KAAA,EACA,iBAAA,EAC2C;QAC3C,OAAO;YACL;YACA,mBAAmB;YACnB,SAAS;gBACP;gBACA,gBAAgB,QAAQ,OAAO,UAAU;gBACzC,WAAW,QAAQ,OAAO,YAAY,SAAS;gBAC/C,UAAU,QAAQ,OAAO,YAAY,QAAQ;gBAC7C,WAAW,QAAQ,OAAO,YAAY,SAAS;gBAC/C,GAAG,iBAAA;YACL;QACF;IACF;AACF;AAYO,SAAS,8BACd,SAAA,EACA,KAAA,EACA,iBAAA,EAC2C;IAC3C,OAAO,6BAA6B,uBAAuB,EAAE,WAAW,OAAO,iBAAiB;AAClG;AAYO,SAAS,6BACd,SAAA,EACA,KAAA,EACA,iBAAA,EAC2C;IAC3C,OAAO,6BAA6B,sBAAsB,EAAE,WAAW,OAAO,iBAAiB;AACjG;AAaO,SAAS,sBACd,SAAA,EACA,QAAsC,CAAC,CAAA,EACG;IAC1C,OAAO;QACL,OAAO;QACP,mBAAmB;QACnB,SAAS;YACP;YACA,GAAG,KAAA;QACL;IACF;AACF;;ACjGA,IAAM,sBAAsB;AASrB,SAAS,kBACd,MAAA,EACA,OAAA,EACsC;IACtC,OAAO;QACL,OAAO;QACP,SAAS;YACP;YACA,GAAG,OAAA;QACL;IACF;AACF;;ACpBA,IAAM,2BAA2B;AACjC,IAAMA,uBAAsB;AAOrB,SAAS,uBAAuB,OAAA,EAA4E;IACjH,OAAO;QACL,OAAO;QACP,mBAAmBA;QACnB;IACF;AACF", "ignoreList": [0, 1, 2, 3, 4], "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bbackend%401.33.0_react_faf293b1b908462ede200692940d58e0/node_modules/%40clerk/backend/src/index.ts"], "sourcesContent": ["import type { TelemetryCollectorOptions } from '@clerk/shared/telemetry';\nimport { TelemetryCollector } from '@clerk/shared/telemetry';\nimport type { SDKMetadata } from '@clerk/types';\n\nimport type { ApiClient, CreateBackendApiOptions } from './api';\nimport { createBackendApiClient } from './api';\nimport { withLegacyReturn } from './jwt/legacyReturn';\nimport type { CreateAuthenticateRequestOptions } from './tokens/factory';\nimport { createAuthenticateRequest } from './tokens/factory';\nimport { verifyToken as _verifyToken } from './tokens/verify';\n\nexport const verifyToken = withLegacyReturn(_verifyToken);\n\nexport type ClerkOptions = CreateBackendApiOptions &\n  Partial<\n    Pick<\n      CreateAuthenticateRequestOptions['options'],\n      'audience' | 'jwtKey' | 'proxyUrl' | 'secretKey' | 'publishableKey' | 'domain' | 'isSatellite'\n    >\n  > & { sdkMetadata?: SDKMetadata; telemetry?: Pick<TelemetryCollectorOptions, 'disabled' | 'debug'> };\n\n// The current exported type resolves the following issue in packages importing createClerkClient\n// TS4023: Exported variable 'clerkClient' has or is using name 'AuthErrorReason' from external module \"/packages/backend/dist/index\" but cannot be named.\nexport type ClerkClient = {\n  telemetry: TelemetryCollector;\n} & ApiClient &\n  ReturnType<typeof createAuthenticateRequest>;\n\nexport function createClerkClient(options: ClerkOptions): ClerkClient {\n  const opts = { ...options };\n  const apiClient = createBackendApiClient(opts);\n  const requestState = createAuthenticateRequest({ options: opts, apiClient });\n  const telemetry = new TelemetryCollector({\n    ...options.telemetry,\n    publishableKey: opts.publishableKey,\n    secretKey: opts.secretKey,\n    samplingRate: 0.1,\n    ...(opts.sdkMetadata ? { sdk: opts.sdkMetadata.name, sdkVersion: opts.sdkMetadata.version } : {}),\n  });\n\n  return {\n    ...apiClient,\n    ...requestState,\n    telemetry,\n  };\n}\n\n/**\n * General Types\n */\nexport type { OrganizationMembershipRole } from './api/resources';\nexport type { VerifyTokenOptions } from './tokens/verify';\n/**\n * JSON types\n */\nexport type {\n  ActorTokenJSON,\n  AccountlessApplicationJSON,\n  ClerkResourceJSON,\n  TokenJSON,\n  AllowlistIdentifierJSON,\n  BlocklistIdentifierJSON,\n  ClientJSON,\n  CnameTargetJSON,\n  DomainJSON,\n  EmailJSON,\n  EmailAddressJSON,\n  ExternalAccountJSON,\n  IdentificationLinkJSON,\n  InstanceJSON,\n  InstanceRestrictionsJSON,\n  InstanceSettingsJSON,\n  InvitationJSON,\n  JwtTemplateJSON,\n  OauthAccessTokenJSON,\n  OAuthApplicationJSON,\n  OrganizationJSON,\n  OrganizationDomainJSON,\n  OrganizationDomainVerificationJSON,\n  OrganizationInvitationJSON,\n  OrganizationSettingsJSON,\n  PublicOrganizationDataJSON,\n  OrganizationMembershipJSON,\n  OrganizationMembershipPublicUserDataJSON,\n  PhoneNumberJSON,\n  ProxyCheckJSON,\n  RedirectUrlJSON,\n  SessionJSON,\n  SignInJSON,\n  SignInTokenJSON,\n  SignUpJSON,\n  SignUpVerificationJSON,\n  SignUpVerificationsJSON,\n  SMSMessageJSON,\n  UserJSON,\n  VerificationJSON,\n  WaitlistEntryJSON,\n  Web3WalletJSON,\n  DeletedObjectJSON,\n  PaginatedResponseJSON,\n  TestingTokenJSON,\n  WebhooksSvixJSON,\n} from './api/resources/JSON';\n\n/**\n * Resources\n */\nexport type {\n  ActorToken,\n  AccountlessApplication,\n  AllowlistIdentifier,\n  BlocklistIdentifier,\n  Client,\n  CnameTarget,\n  Domain,\n  EmailAddress,\n  ExternalAccount,\n  Instance,\n  InstanceRestrictions,\n  InstanceSettings,\n  Invitation,\n  JwtTemplate,\n  OauthAccessToken,\n  OAuthApplication,\n  Organization,\n  OrganizationDomain,\n  OrganizationDomainVerification,\n  OrganizationInvitation,\n  OrganizationMembership,\n  OrganizationMembershipPublicUserData,\n  OrganizationSettings,\n  PhoneNumber,\n  Session,\n  SignInToken,\n  SignUpAttempt,\n  SMSMessage,\n  Token,\n  User,\n  TestingToken,\n} from './api/resources';\n\n/**\n * Webhooks event types\n */\nexport type {\n  EmailWebhookEvent,\n  OrganizationWebhookEvent,\n  OrganizationDomainWebhookEvent,\n  OrganizationInvitationWebhookEvent,\n  OrganizationMembershipWebhookEvent,\n  RoleWebhookEvent,\n  PermissionWebhookEvent,\n  SessionWebhookEvent,\n  SMSWebhookEvent,\n  UserWebhookEvent,\n  WaitlistEntryWebhookEvent,\n  WebhookEvent,\n  WebhookEventType,\n} from './api/resources/Webhooks';\n\n/**\n * Auth objects\n */\nexport type { AuthObject } from './tokens/authObjects';\n"], "names": ["verifyToken"], "mappings": ";;;;;;;;;;;AACA,SAAS,0BAA0B;;;;;;;AAU5B,IAAMA,2RAAc,mBAAA,0QAAiB,cAAY;AAiBjD,SAAS,kBAAkB,OAAA,EAAoC;IACpE,MAAM,OAAO;QAAE,GAAG,OAAA;IAAQ;IAC1B,MAAM,wRAAY,yBAAA,EAAuB,IAAI;IAC7C,MAAM,2RAAe,4BAAA,EAA0B;QAAE,SAAS;QAAM;IAAU,CAAC;IAC3E,MAAM,YAAY,6QAAI,sBAAA,CAAmB;QACvC,GAAG,QAAQ,SAAA;QACX,gBAAgB,KAAK,cAAA;QACrB,WAAW,KAAK,SAAA;QAChB,cAAc;QACd,GAAI,KAAK,WAAA,GAAc;YAAE,KAAK,KAAK,WAAA,CAAY,IAAA;YAAM,YAAY,KAAK,WAAA,CAAY,OAAA;QAAQ,IAAI,CAAC,CAAA;IACjG,CAAC;IAED,OAAO;QACL,GAAG,SAAA;QACH,GAAG,YAAA;QACH;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/createClerkClient.ts"], "sourcesContent": ["import { createClerkClient } from '@clerk/backend';\n\nimport {\n  API_URL,\n  API_VERSION,\n  DOMAIN,\n  IS_SATELLITE,\n  PROXY_URL,\n  PUBLISHABLE_KEY,\n  SDK_METADATA,\n  SECRET_KEY,\n  TELEMETRY_DEBUG,\n  TELEMETRY_DISABLED,\n} from './constants';\n\nconst clerkClientDefaultOptions = {\n  secretKey: SECRET_KEY,\n  publishableKey: PUBLISHABLE_KEY,\n  apiUrl: API_URL,\n  apiVersion: API_VERSION,\n  userAgent: `${PACKAGE_NAME}@${PACKAGE_VERSION}`,\n  proxyUrl: PROXY_URL,\n  domain: DOMAIN,\n  isSatellite: IS_SATELLITE,\n  sdkMetadata: SDK_METADATA,\n  telemetry: {\n    disabled: TELEMETRY_DISABLED,\n    debug: TELEMETRY_DEBUG,\n  },\n};\n\nexport const createClerkClientWithOptions: typeof createClerkClient = options =>\n  createClerkClient({ ...clerkClientDefaultOptions, ...options });\n"], "names": [], "mappings": ";;;AAAA,SAAS,yBAAyB;AAElC;;;;AAaA,MAAM,4BAA4B;IAChC,6RAAW,aAAA;IACX,kSAAgB,kBAAA;IAChB,0RAAQ,UAAA;IACR,6RAAY,eAAA;IACZ,WAAW,GAAG,eAAY,CAAA,CAAA,EAAI,QAAe,EAAA;IAC7C,4RAAU,YAAA;IACV,0RAAQ,SAAA;IACR,aAAa,iSAAA;IACb,+RAAa,eAAA;IACb,WAAW;QACT,4RAAU,qBAAA;QACV,yRAAO,kBAAA;IACT;AACF;AAEO,MAAM,+BAAyD,CAAA,0QACpE,oBAAA,EAAkB;QAAE,GAAG,yBAAA;QAA2B,GAAG,OAAA;IAAQ,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 444, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/runtime/node/safe-node-apis.js"], "sourcesContent": ["/**\n * This file is used for conditional imports to mitigate bundling issues with Next.js server actions on version prior to 14.1.0.\n */\n// eslint-disable-next-line @typescript-eslint/no-require-imports\nconst { existsSync, writeFileSync, readFileSync, appendFileSync, mkdirSync, rmSync } = require('node:fs');\n// eslint-disable-next-line @typescript-eslint/no-require-imports\nconst path = require('node:path');\nconst fs = {\n  existsSync,\n  writeFileSync,\n  readFileSync,\n  appendFileSync,\n  mkdirSync,\n  rmSync,\n};\n\nconst cwd = () => process.cwd();\n\nmodule.exports = { fs, path, cwd };\n"], "names": [], "mappings": ";;;;;AAAA,IAAA,yBAAA,CAAA,GAAA,+QAAA,CAAA,aAAA,EAAA;IAAA,sCAAA,OAAA,EAAA,MAAA;QAIA,MAAM,EAAE,UAAA,EAAY,aAAA,EAAe,YAAA,EAAc,cAAA,EAAgB,SAAA,EAAW,MAAA,CAAO,CAAA,GAAI,QAAQ,SAAS;QAExG,MAAM,OAAO,QAAQ,WAAW;QAChC,MAAM,KAAK;YACT;YACA;YACA;YACA;YACA;YACA;QACF;QAEA,MAAM,MAAM,IAAM,QAAQ,GAAA,CAAI;QAE9B,OAAO,OAAA,GAAU;YAAE;YAAI;YAAM;QAAI;IAAA;AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/fs/utils.ts"], "sourcesContent": ["/**\n * Attention: Only import this module when the node runtime is used.\n * We are using conditional imports to mitigate bundling issues with Next.js server actions on version prior to 14.1.0.\n */\n// @ts-ignore\nimport nodeRuntime from '#safe-node-apis';\n\nconst throwMissingFsModule = (module: string) => {\n  throw new Error(`Clerk: ${module} is missing. This is an internal error. Please contact Clerk's support.`);\n};\n\nconst nodeFsOrThrow = () => {\n  if (!nodeRuntime.fs) {\n    throwMissingFsModule('fs');\n  }\n  return nodeRuntime.fs;\n};\n\nconst nodePathOrThrow = () => {\n  if (!nodeRuntime.path) {\n    throwMissingFsModule('path');\n  }\n  return nodeRuntime.path;\n};\n\nconst nodeCwdOrThrow = () => {\n  if (!nodeRuntime.cwd) {\n    throwMissingFsModule('cwd');\n  }\n  return nodeRuntime.cwd;\n};\n\nexport { nodeCwdOrThrow, nodeFsOrThrow, nodePathOrThrow };\n"], "names": [], "mappings": ";;;;;AAKA,OAAO,iBAAiB;;;AAExB,MAAM,uBAAuB,CAAC,WAAmB;IAC/C,MAAM,IAAI,MAAM,CAAA,OAAA,EAAU,MAAM,CAAA,uEAAA,CAAyE;AAC3G;AAEA,MAAM,gBAAgB,MAAM;IAC1B,IAAI,uSAAC,UAAA,CAAY,EAAA,EAAI;QACnB,qBAAqB,IAAI;IAC3B;IACA,6SAAO,UAAA,CAAY,EAAA;AACrB;AAEA,MAAM,kBAAkB,MAAM;IAC5B,IAAI,uSAAC,UAAA,CAAY,IAAA,EAAM;QACrB,qBAAqB,MAAM;IAC7B;IACA,6SAAO,UAAA,CAAY,IAAA;AACrB;AAEA,MAAM,iBAAiB,MAAM;IAC3B,IAAI,uSAAC,UAAA,CAAY,GAAA,EAAK;QACpB,qBAAqB,KAAK;IAC5B;IACA,6SAAO,UAAA,CAAY,GAAA;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 514, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/keyless-node.ts"], "sourcesContent": ["import type { AccountlessApplication } from '@clerk/backend';\n\nimport { createClerkClientWithOptions } from './createClerkClient';\nimport { nodeCwdOrThrow, nodeFsOrThrow, nodePathOrThrow } from './fs/utils';\n\n/**\n * The Clerk-specific directory name.\n */\nconst CLERK_HIDDEN = '.clerk';\n\n/**\n * The Clerk-specific lock file that is used to mitigate multiple key creation.\n * This is automatically cleaned up.\n */\nconst CLERK_LOCK = 'clerk.lock';\n\n/**\n * The `.clerk/` directory is NOT safe to be committed as it may include sensitive information about a Clerk instance.\n * It may include an instance's secret key and the secret token for claiming that instance.\n */\nfunction updateGitignore() {\n  const { existsSync, writeFileSync, readFileSync, appendFileSync } = nodeFsOrThrow();\n\n  const path = nodePathOrThrow();\n  const cwd = nodeCwdOrThrow();\n  const gitignorePath = path.join(cwd(), '.gitignore');\n  if (!existsSync(gitignorePath)) {\n    writeFileSync(gitignorePath, '');\n  }\n\n  // Check if `.clerk/` entry exists in .gitignore\n  const gitignoreContent = readFileSync(gitignorePath, 'utf-8');\n  const COMMENT = `# clerk configuration (can include secrets)`;\n  if (!gitignoreContent.includes(CLERK_HIDDEN + '/')) {\n    appendFileSync(gitignorePath, `\\n${COMMENT}\\n/${CLERK_HIDDEN}/\\n`);\n  }\n}\n\nconst generatePath = (...slugs: string[]) => {\n  const path = nodePathOrThrow();\n  const cwd = nodeCwdOrThrow();\n  return path.join(cwd(), CLERK_HIDDEN, ...slugs);\n};\n\nconst _TEMP_DIR_NAME = '.tmp';\nconst getKeylessConfigurationPath = () => generatePath(_TEMP_DIR_NAME, 'keyless.json');\nconst getKeylessReadMePath = () => generatePath(_TEMP_DIR_NAME, 'README.md');\n\nlet isCreatingFile = false;\n\nexport function safeParseClerkFile(): AccountlessApplication | undefined {\n  const { readFileSync } = nodeFsOrThrow();\n  try {\n    const CONFIG_PATH = getKeylessConfigurationPath();\n    let fileAsString;\n    try {\n      fileAsString = readFileSync(CONFIG_PATH, { encoding: 'utf-8' }) || '{}';\n    } catch {\n      fileAsString = '{}';\n    }\n    return JSON.parse(fileAsString) as AccountlessApplication;\n  } catch {\n    return undefined;\n  }\n}\n\n/**\n * Using both an in-memory and file system lock seems to be the most effective solution.\n */\nconst lockFileWriting = () => {\n  const { writeFileSync } = nodeFsOrThrow();\n\n  isCreatingFile = true;\n\n  writeFileSync(\n    CLERK_LOCK,\n    // In the rare case, the file persists give the developer enough context.\n    'This file can be deleted. Please delete this file and refresh your application',\n    {\n      encoding: 'utf8',\n      mode: '0777',\n      flag: 'w',\n    },\n  );\n};\n\nconst unlockFileWriting = () => {\n  const { rmSync } = nodeFsOrThrow();\n\n  try {\n    rmSync(CLERK_LOCK, { force: true, recursive: true });\n  } catch {\n    // Simply ignore if the removal of the directory/file fails\n  }\n\n  isCreatingFile = false;\n};\n\nconst isFileWritingLocked = () => {\n  const { existsSync } = nodeFsOrThrow();\n  return isCreatingFile || existsSync(CLERK_LOCK);\n};\n\nasync function createOrReadKeyless(): Promise<AccountlessApplication | null> {\n  const { writeFileSync, mkdirSync } = nodeFsOrThrow();\n\n  /**\n   * If another request is already in the process of acquiring keys return early.\n   * Using both an in-memory and file system lock seems to be the most effective solution.\n   */\n  if (isFileWritingLocked()) {\n    return null;\n  }\n\n  lockFileWriting();\n\n  const CONFIG_PATH = getKeylessConfigurationPath();\n  const README_PATH = getKeylessReadMePath();\n\n  mkdirSync(generatePath(_TEMP_DIR_NAME), { recursive: true });\n  updateGitignore();\n\n  /**\n   * When the configuration file exists, always read the keys from the file\n   */\n  const envVarsMap = safeParseClerkFile();\n  if (envVarsMap?.publishableKey && envVarsMap?.secretKey) {\n    unlockFileWriting();\n\n    return envVarsMap;\n  }\n\n  /**\n   * At this step, it is safe to create new keys and store them.\n   */\n  const client = createClerkClientWithOptions({});\n  const accountlessApplication = await client.__experimental_accountlessApplications\n    .createAccountlessApplication()\n    .catch(() => null);\n\n  if (accountlessApplication) {\n    writeFileSync(CONFIG_PATH, JSON.stringify(accountlessApplication), {\n      encoding: 'utf8',\n      mode: '0777',\n      flag: 'w',\n    });\n\n    // TODO-KEYLESS: Add link to official documentation.\n    const README_NOTIFICATION = `\n## DO NOT COMMIT\nThis directory is auto-generated from \\`@clerk/nextjs\\` because you are running in Keyless mode. Avoid committing the \\`.clerk/\\` directory as it includes the secret key of the unclaimed instance.\n  `;\n\n    writeFileSync(README_PATH, README_NOTIFICATION, {\n      encoding: 'utf8',\n      mode: '0777',\n      flag: 'w',\n    });\n  }\n  /**\n   * Clean up locks.\n   */\n  unlockFileWriting();\n\n  return accountlessApplication;\n}\n\nfunction removeKeyless() {\n  const { rmSync } = nodeFsOrThrow();\n\n  /**\n   * If another request is already in the process of acquiring keys return early.\n   * Using both an in-memory and file system lock seems to be the most effective solution.\n   */\n  if (isFileWritingLocked()) {\n    return undefined;\n  }\n\n  lockFileWriting();\n\n  try {\n    rmSync(generatePath(), { force: true, recursive: true });\n  } catch {\n    // Simply ignore if the removal of the directory/file fails\n  }\n\n  /**\n   * Clean up locks.\n   */\n  unlockFileWriting();\n}\n\nexport { createOrReadKeyless, removeKeyless };\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,oCAAoC;AAC7C,SAAS,gBAAgB,eAAe,uBAAuB;;;;AAK/D,MAAM,eAAe;AAMrB,MAAM,aAAa;AAMnB,SAAS,kBAAkB;IACzB,MAAM,EAAE,UAAA,EAAY,aAAA,EAAe,YAAA,EAAc,cAAA,CAAe,CAAA,0RAAI,iBAAA,CAAc;IAElF,MAAM,+RAAO,kBAAA,CAAgB;IAC7B,MAAM,6RAAM,kBAAA,CAAe;IAC3B,MAAM,gBAAgB,KAAK,IAAA,CAAK,IAAI,GAAG,YAAY;IACnD,IAAI,CAAC,WAAW,aAAa,GAAG;QAC9B,cAAc,eAAe,EAAE;IACjC;IAGA,MAAM,mBAAmB,aAAa,eAAe,OAAO;IAC5D,MAAM,UAAU,CAAA,2CAAA,CAAA;IAChB,IAAI,CAAC,iBAAiB,QAAA,CAAS,eAAe,GAAG,GAAG;QAClD,eAAe,eAAe,CAAA;AAAA,EAAK,OAAO,CAAA;CAAA,EAAM,YAAY,CAAA;AAAA,CAAK;IACnE;AACF;AAEA,MAAM,eAAe,CAAA,GAAI,UAAoB;IAC3C,MAAM,WAAO,sSAAA,CAAgB;IAC7B,MAAM,8RAAM,iBAAA,CAAe;IAC3B,OAAO,KAAK,IAAA,CAAK,IAAI,GAAG,cAAc,GAAG,KAAK;AAChD;AAEA,MAAM,iBAAiB;AACvB,MAAM,8BAA8B,IAAM,aAAa,gBAAgB,cAAc;AACrF,MAAM,uBAAuB,IAAM,aAAa,gBAAgB,WAAW;AAE3E,IAAI,iBAAiB;AAEd,SAAS,qBAAyD;IACvE,MAAM,EAAE,YAAA,CAAa,CAAA,2RAAI,gBAAA,CAAc;IACvC,IAAI;QACF,MAAM,cAAc,4BAA4B;QAChD,IAAI;QACJ,IAAI;YACF,eAAe,aAAa,aAAa;gBAAE,UAAU;YAAQ,CAAC,KAAK;QACrE,EAAA,OAAQ;YACN,eAAe;QACjB;QACA,OAAO,KAAK,KAAA,CAAM,YAAY;IAChC,EAAA,OAAQ;QACN,OAAO,KAAA;IACT;AACF;AAKA,MAAM,kBAAkB,MAAM;IAC5B,MAAM,EAAE,aAAA,CAAc,CAAA,2RAAI,gBAAA,CAAc;IAExC,iBAAiB;IAEjB,cACE,YAAA,yEAAA;IAEA,kFACA;QACE,UAAU;QACV,MAAM;QACN,MAAM;IACR;AAEJ;AAEA,MAAM,oBAAoB,MAAM;IAC9B,MAAM,EAAE,MAAA,CAAO,CAAA,2RAAI,gBAAA,CAAc;IAEjC,IAAI;QACF,OAAO,YAAY;YAAE,OAAO;YAAM,WAAW;QAAK,CAAC;IACrD,EAAA,OAAQ,CAER;IAEA,iBAAiB;AACnB;AAEA,MAAM,sBAAsB,MAAM;IAChC,MAAM,EAAE,UAAA,CAAW,CAAA,2RAAI,gBAAA,CAAc;IACrC,OAAO,kBAAkB,WAAW,UAAU;AAChD;AAEA,eAAe,sBAA8D;IAC3E,MAAM,EAAE,aAAA,EAAe,SAAA,CAAU,CAAA,2RAAI,gBAAA,CAAc;IAMnD,IAAI,oBAAoB,GAAG;QACzB,OAAO;IACT;IAEA,gBAAgB;IAEhB,MAAM,cAAc,4BAA4B;IAChD,MAAM,cAAc,qBAAqB;IAEzC,UAAU,aAAa,cAAc,GAAG;QAAE,WAAW;IAAK,CAAC;IAC3D,gBAAgB;IAKhB,MAAM,aAAa,mBAAmB;IACtC,IAAA,CAAI,cAAA,OAAA,KAAA,IAAA,WAAY,cAAA,KAAA,CAAkB,cAAA,OAAA,KAAA,IAAA,WAAY,SAAA,GAAW;QACvD,kBAAkB;QAElB,OAAO;IACT;IAKA,MAAM,uSAAS,+BAAA,EAA6B,CAAC,CAAC;IAC9C,MAAM,yBAAyB,MAAM,OAAO,sCAAA,CACzC,4BAAA,CAA6B,EAC7B,KAAA,CAAM,IAAM,IAAI;IAEnB,IAAI,wBAAwB;QAC1B,cAAc,aAAa,KAAK,SAAA,CAAU,sBAAsB,GAAG;YACjE,UAAU;YACV,MAAM;YACN,MAAM;QACR,CAAC;QAGD,MAAM,sBAAsB,CAAA;;;EAAA,CAAA;QAK5B,cAAc,aAAa,qBAAqB;YAC9C,UAAU;YACV,MAAM;YACN,MAAM;QACR,CAAC;IACH;IAIA,kBAAkB;IAElB,OAAO;AACT;AAEA,SAAS,gBAAgB;IACvB,MAAM,EAAE,MAAA,CAAO,CAAA,2RAAI,gBAAA,CAAc;IAMjC,IAAI,oBAAoB,GAAG;QACzB,OAAO,KAAA;IACT;IAEA,gBAAgB;IAEhB,IAAI;QACF,OAAO,aAAa,GAAG;YAAE,OAAO;YAAM,WAAW;QAAK,CAAC;IACzD,EAAA,OAAQ,CAER;IAKA,kBAAkB;AACpB", "ignoreList": [0], "debugId": null}}]}