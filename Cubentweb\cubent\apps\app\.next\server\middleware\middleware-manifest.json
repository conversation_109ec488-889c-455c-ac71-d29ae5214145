{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_ec43db50._.js", "server/edge/chunks/eec21_@clerk_shared_dist_40b2e982._.js", "server/edge/chunks/c67f4_@clerk_backend_dist_d8cc056d._.js", "server/edge/chunks/25c57_@clerk_nextjs_dist_esm_1ca17405._.js", "server/edge/chunks/ec4b9_zod_dist_esm_cbcb71bd._.js", "server/edge/chunks/node_modules__pnpm_16b65189._.js", "server/edge/chunks/[root-of-the-server]__a70c1ea8._.js", "server/edge/chunks/apps_app_edge-wrapper_e3ae19de.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*){(\\\\.json)}?", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(api|trpc)(.*){(\\\\.json)}?", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "PsOk9cbLj57mzbP7S5dexg03XmOiex4+AQ0DyfIt1KU=", "__NEXT_PREVIEW_MODE_ID": "cd7ef82b3ab63bb0a44094818284c4b5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4bd2663face5ca2b0789f606ba47286f0456464e0fd6b260101fab26be833d6f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c8252670b7260291971d9e9288e23c95dbdf50e9129e812e298fbcda9c4f0870"}}}, "instrumentation": null, "functions": {}}