{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/9583c_@sentry_core_build_esm_1adfbc63._.js", "server/edge/chunks/4b803_@sentry_vercel-edge_build_esm_index_de3a4916.js", "server/edge/chunks/ec4b9_zod_dist_esm_d6b871ea._.js", "server/edge/chunks/node_modules__pnpm_096b6372._.js", "server/edge/chunks/[root-of-the-server]__85fdb81a._.js", "server/edge/chunks/apps_app_edge-wrapper_7660c19f.js", "server/edge/chunks/_ec43db50._.js", "server/edge/chunks/eec21_@clerk_shared_dist_40b2e982._.js", "server/edge/chunks/c67f4_@clerk_backend_dist_d8cc056d._.js", "server/edge/chunks/25c57_@clerk_nextjs_dist_esm_1ca17405._.js", "server/edge/chunks/ec4b9_zod_dist_esm_cbcb71bd._.js", "server/edge/chunks/node_modules__pnpm_16b65189._.js", "server/edge/chunks/[root-of-the-server]__a70c1ea8._.js", "server/edge/chunks/apps_app_edge-wrapper_e3ae19de.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "PsOk9cbLj57mzbP7S5dexg03XmOiex4+AQ0DyfIt1KU=", "__NEXT_PREVIEW_MODE_ID": "cd7ef82b3ab63bb0a44094818284c4b5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4bd2663face5ca2b0789f606ba47286f0456464e0fd6b260101fab26be833d6f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c8252670b7260291971d9e9288e23c95dbdf50e9129e812e298fbcda9c4f0870"}}}, "sortedMiddleware": ["/"], "functions": {"/icon/route": {"files": ["server/edge/chunks/9583c_@sentry_core_build_esm_1adfbc63._.js", "server/edge/chunks/4b803_@sentry_vercel-edge_build_esm_index_de3a4916.js", "server/edge/chunks/ec4b9_zod_dist_esm_d6b871ea._.js", "server/edge/chunks/node_modules__pnpm_096b6372._.js", "server/edge/chunks/[root-of-the-server]__85fdb81a._.js", "server/edge/chunks/apps_app_edge-wrapper_7660c19f.js", "server/server-reference-manifest.js", "server/middleware-build-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/app/icon/route_client-reference-manifest.js", "server/edge/chunks/apps_app__next-internal_server_app_icon_route_actions_19875a25.js", "server/edge/chunks/apps_app__next-internal_server_app_icon_route_actions_c586e85e.js", "server/edge/chunks/661a5_next_dist_esm_294fa881._.js", "server/edge/chunks/661a5_next_dist_compiled_react-server-dom-turbopack_34e1695d._.js", "server/edge/chunks/661a5_next_dist_compiled_@vercel_og_225bb2b9._.js", "server/edge/chunks/661a5_next_dist_compiled_f39918aa._.js", "server/edge/chunks/661a5_next_dist_025236e0._.js", "server/edge/chunks/apps_app_edge-wrapper_fbda58b4.js", "server/edge/chunks/[root-of-the-server]__a75cb303._.js", "server/edge/chunks/apps_app_edge-wrapper_338ba5da.js", "server/app/icon/route/react-loadable-manifest.js"], "name": "/icon", "page": "/icon/route", "matchers": [{"regexp": "^/icon(?:/)?$", "originalSource": "/icon"}], "wasm": [{"name": "wasm_661a5_next_dist_compiled__vercel_og_yoga_325b988e", "filePath": "server/edge/chunks/661a5_next_dist_compiled_@vercel_og_yoga_325b988e.wasm"}, {"name": "wasm_661a5_next_dist_compiled__vercel_og_resvg_325b988e", "filePath": "server/edge/chunks/661a5_next_dist_compiled_@vercel_og_resvg_325b988e.wasm"}], "assets": [{"name": "server/edge/assets/noto-sans-v27-latin-regular.7e1666d1.ttf", "filePath": "server/edge/assets/noto-sans-v27-latin-regular.7e1666d1.ttf"}], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "PsOk9cbLj57mzbP7S5dexg03XmOiex4+AQ0DyfIt1KU=", "__NEXT_PREVIEW_MODE_ID": "cd7ef82b3ab63bb0a44094818284c4b5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4bd2663face5ca2b0789f606ba47286f0456464e0fd6b260101fab26be833d6f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c8252670b7260291971d9e9288e23c95dbdf50e9129e812e298fbcda9c4f0870"}}}}