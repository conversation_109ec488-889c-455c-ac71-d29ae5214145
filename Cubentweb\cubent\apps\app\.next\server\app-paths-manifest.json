{"/(authenticated)/auth-success/page": "app/(authenticated)/auth-success/page.js", "/(authenticated)/dashboard/page": "app/(authenticated)/dashboard/page.js", "/(authenticated)/page": "app/(authenticated)/page.js", "/(authenticated)/profile/page": "app/(authenticated)/profile/page.js", "/(authenticated)/usage/requests/page": "app/(authenticated)/usage/requests/page.js", "/(unauthenticated)/sign-in/[[...sign-in]]/page": "app/(unauthenticated)/sign-in/[[...sign-in]]/page.js", "/api/auth/clear-cross-domain-token/route": "app/api/auth/clear-cross-domain-token/route.js", "/api/auth/set-cross-domain-token/route": "app/api/auth/set-cross-domain-token/route.js", "/api/extension/status/route": "app/api/extension/status/route.js", "/icon.png/route": "app/icon.png/route.js", "/icon/route": "app-edge-has-no-entrypoint"}