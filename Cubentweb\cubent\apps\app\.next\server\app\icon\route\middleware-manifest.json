{"sorted_middleware": ["/icon/route"], "middleware": {}, "instrumentation": null, "functions": {"/icon/route": {"files": ["server/server-reference-manifest.js", "server/middleware-build-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/app/icon/route_client-reference-manifest.js", "server/edge/chunks/apps_app__next-internal_server_app_icon_route_actions_19875a25.js", "server/edge/chunks/apps_app__next-internal_server_app_icon_route_actions_c586e85e.js", "server/edge/chunks/661a5_next_dist_esm_294fa881._.js", "server/edge/chunks/661a5_next_dist_compiled_react-server-dom-turbopack_34e1695d._.js", "server/edge/chunks/661a5_next_dist_compiled_@vercel_og_225bb2b9._.js", "server/edge/chunks/661a5_next_dist_compiled_f39918aa._.js", "server/edge/chunks/661a5_next_dist_025236e0._.js", "server/edge/chunks/apps_app_edge-wrapper_fbda58b4.js", "server/edge/chunks/[root-of-the-server]__a75cb303._.js", "server/edge/chunks/apps_app_edge-wrapper_338ba5da.js", "server/app/icon/route/react-loadable-manifest.js"], "name": "/icon", "page": "/icon/route", "matchers": [{"regexp": "^/icon(?:/)?$", "originalSource": "/icon"}], "wasm": [{"name": "wasm_661a5_next_dist_compiled__vercel_og_resvg_325b988e", "filePath": "server/edge/chunks/661a5_next_dist_compiled_@vercel_og_resvg_325b988e.wasm"}, {"name": "wasm_661a5_next_dist_compiled__vercel_og_yoga_325b988e", "filePath": "server/edge/chunks/661a5_next_dist_compiled_@vercel_og_yoga_325b988e.wasm"}], "assets": [{"name": "server/edge/assets/noto-sans-v27-latin-regular.7e1666d1.ttf", "filePath": "server/edge/assets/noto-sans-v27-latin-regular.7e1666d1.ttf"}], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "PsOk9cbLj57mzbP7S5dexg03XmOiex4+AQ0DyfIt1KU=", "__NEXT_PREVIEW_MODE_ID": "5b9d61d99a4bc6608b129d8d029bc6ee", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "afc97321ee45986adbe58c158cea85f187ecac46c59923af438063d5a68ad536", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "41a4a1c5e4f44502d494e88c18ec3ad5dec9d5b252a0327be4ee100bc1f12c47"}}}}